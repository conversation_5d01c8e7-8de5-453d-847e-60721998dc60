/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.debug;

/**
 * Contains information about the client in an ADB connection.
 * @see {@link android.debug.IAdbManager#getPairedDevices()}
 * @hide
 */
parcelable PairDevice {
    /**
     * The human-readable name of the device.
     */
    String name;

    /**
     * The device's guid.
     */
    String guid;

    /**
     * Indicates whether the device is currently connected to adbd.
     */
    boolean connected;
}