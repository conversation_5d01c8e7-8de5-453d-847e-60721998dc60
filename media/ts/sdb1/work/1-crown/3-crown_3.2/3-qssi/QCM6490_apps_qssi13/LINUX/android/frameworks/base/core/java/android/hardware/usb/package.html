<HTML>
<BODY>
<p>Provides support to communicate with USB hardware peripherals that are connected to 
Android-powered devices.</p>

<p>For more information, see the
<a href="{@docRoot}guide/topics/connectivity/usb/index.html">USB</a> guide.</p>
{@more}

<p>Use {@link android.hardware.usb.UsbManager} to access the state of the USB and to
communicate with connected hardware peripherals. Use {@link android.hardware.usb.UsbDevice} to
communicate with the hardware peripheral if the Android-powered device is acting as the USB host.
Use {@link android.hardware.usb.UsbAccessory} if the peripheral is acting as the USB host.</p>

</BODY>
</HTML>