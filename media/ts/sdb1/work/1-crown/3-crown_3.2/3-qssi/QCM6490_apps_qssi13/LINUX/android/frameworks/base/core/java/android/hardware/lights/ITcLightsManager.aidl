package android.hardware.lights;

/**
 * API to TC lights manager service.
 *
 * {@hide}
 */
interface ITcLightsManager {
    int setColor(int priority, int color);
    int setFlashing(int priority, int color, int onMs, int offMs);
    int setFlashingDeviceError(int priority);
    int setColorById(int id, int color);
    int setFlashingById(int id, int color, int onMs, int offMs);
    int turnOff(int id);
    int turnOffAll();
}