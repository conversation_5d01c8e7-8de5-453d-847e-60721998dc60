/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package android.hardware.biometrics;

/**
 * Communication channel from <Biometric>Service back to BiometricService.
 * @hide
 */
oneway interface IBiometricSensorReceiver {
    // Notify BiometricService that authentication was successful. If user confirmation is required,
    // the auth token must be submitted into KeyStore.
    void onAuthenticationSucceeded(int sensorId, in byte[] token);
    // Notify BiometricService authentication was rejected.
    void onAuthenticationFailed(int sensorId);
    // Notify BiometricService than an error has occured. Forward to the correct receiver depending
    // on the cookie.
    void onError(int sensorId, int cookie, int error, int vendorCode);
    // Notifies that a biometric has been acquired.
    void onAcquired(int sensorId, int acquiredInfo, int vendorCode);
}
