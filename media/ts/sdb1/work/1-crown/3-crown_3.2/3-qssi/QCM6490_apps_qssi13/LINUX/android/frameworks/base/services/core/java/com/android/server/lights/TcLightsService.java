package com.android.server.lights;

import static android.hardware.lights.TcLightsManager.LED_COLOR_BLUE;
import static android.hardware.lights.TcLightsManager.LED_COLOR_RED;

import android.content.Context;
import android.hardware.lights.ITcLightsManager;
import android.hardware.lights.TcLightsManager;
import android.util.Slog;

import com.android.server.SystemService;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TcLightsService extends SystemService {
    private static final String TAG = "TcLightsService";

    private final TcLightsManagerBinderService mManagerService;
    private final LightsManager mLightsManager;
    private final ConcurrentHashMap<Integer, TcLightSession> mTcLightSessionMap
            = new ConcurrentHashMap<>();
    private TcLightSession mActiveLightSession;

    private boolean isManySession() {
        return mTcLightSessionMap.size() > TcLightsManager.MAX_SESSION;
    }

    /**
     * Get current active session by priority.
     *
     * @return session
     */
    private TcLightSession getActivePrioritySession() {
        try {
            Optional<Map.Entry<Integer, TcLightSession>> session = mTcLightSessionMap.entrySet()
                    .stream().max(Map.Entry.comparingByKey());
            return session.isEmpty() ? null : session.get().getValue();
        } catch (Exception ex) {
            Slog.e(TAG, "getActivePrioritySession failed", ex);
        }
        return null;
    }

    private void removeSessionById(int id) {
        if (mTcLightSessionMap.containsKey(id)) {
            mTcLightSessionMap.remove(id);
        } else {
            Slog.d(TAG, "removeSessionById failed, session is not found");
        }
    }

    private TcLightSession getSessionById(int id) {
        if (mTcLightSessionMap.containsKey(id)) {
            return mTcLightSessionMap.get(id);
        } else {
            Slog.d(TAG, "getSessionById failed, session is not found");
        }
        return null;
    }

    private final class TcLightSession {
        static final int SESSION_MODE_COLOR = 0;
        static final int SESSION_MODE_FLASHING = 1;
        static final int SESSION_MODE_FLASHING_DEVICE_ERROR = 2;
        int priority;
        int sessionMode;
        int color;
        int onMs;
        int offMs;
        int id;

        public int getPriority() {
            return priority;
        }

        public TcLightSession() {
        }

        public TcLightSession(int priority, int color) {
            this.sessionMode = SESSION_MODE_COLOR;
            this.id = priority;
            this.priority = priority;
            this.color = color;
            mTcLightSessionMap.put(priority, this);
        }

        public TcLightSession(int priority, int color, int onMs, int offMs) {
            this.sessionMode = SESSION_MODE_FLASHING;
            this.id = priority;
            this.priority = priority;
            this.color = color;
            this.onMs = onMs;
            this.offMs = offMs;
            mTcLightSessionMap.put(priority, this);
        }

        public TcLightSession(int priority) {
            this.sessionMode = SESSION_MODE_FLASHING_DEVICE_ERROR;
            this.id = priority;
            this.priority = priority;
            mTcLightSessionMap.put(priority, this);
        }
    }

    private final class TcLightsManagerBinderService extends ITcLightsManager.Stub {

        private ScheduledExecutorService mDeviceErrorExecutor;
        private int mDeviceErrorColor = LED_COLOR_BLUE;

        @Override
        public int setColor(int priority, int color) {
            synchronized (TcLightsService.this) {
                if (priority < TcLightsManager.MIN_PRIORITY) {
                    return TcLightsManager.ERROR_PRIORITY_MUST_MORE_THAN_ONE;
                }
                if (isManySession()) {
                    return TcLightsManager.ERROR_TOO_MANY_SESSION;
                }
                TcLightSession session = new TcLightSession(priority, color);
                mActiveLightSession = getActivePrioritySession();
                if (mActiveLightSession != null) {
                    updateActiveLightSession();
                } else {
                    Slog.e(TAG, "Get active session failed by priority");
                }
                return session.id;
            }
        }

        @Override
        public int setFlashing(int priority, int color, int onMs, int offMs) {
            synchronized (TcLightsService.this) {
                if (priority < TcLightsManager.MIN_PRIORITY) {
                    return TcLightsManager.ERROR_PRIORITY_MUST_MORE_THAN_ONE;
                }
                if (isManySession()) {
                    return TcLightsManager.ERROR_TOO_MANY_SESSION;
                }
                TcLightSession session = new TcLightSession(priority, color, onMs, offMs);
                mActiveLightSession = getActivePrioritySession();
                if (mActiveLightSession != null) {
                    updateActiveLightSession();
                } else {
                    Slog.e(TAG, "Get active session failed by priority");
                }
                return session.id;
            }
        }

        @Override
        public int setFlashingDeviceError(int priority) {
            synchronized (TcLightsService.this) {
                if (priority < TcLightsManager.MIN_PRIORITY) {
                    return TcLightsManager.ERROR_PRIORITY_MUST_MORE_THAN_ONE;
                }
                if (isManySession()) {
                    return TcLightsManager.ERROR_TOO_MANY_SESSION;
                }
                TcLightSession session = new TcLightSession(priority);
                mActiveLightSession = getActivePrioritySession();
                if (mActiveLightSession != null) {
                    updateActiveLightSession();
                } else {
                    Slog.e(TAG, "Get active session failed by priority");
                }
                return session.id;
            }
        }

        private void updateActiveLightSession() {
            if (mActiveLightSession.sessionMode == TcLightSession.SESSION_MODE_FLASHING) {
                setFlashingInternal(mActiveLightSession.color, mActiveLightSession.onMs, mActiveLightSession.offMs);
            } else if (mActiveLightSession.sessionMode == TcLightSession.SESSION_MODE_COLOR) {
                setColorInternal(mActiveLightSession.color);
            } else if (mActiveLightSession.sessionMode == TcLightSession.SESSION_MODE_FLASHING_DEVICE_ERROR) {
                setFlashingDeviceErrorInternal();
            } else {
                Slog.w(TAG, "updateActiveLightSession failed, session mode is not found");
            }
        }

        @Override
        public int setColorById(int id, int color) {
            synchronized (TcLightsService.this) {
                TcLightSession session = getSessionById(id);
                if (session == null) {
                    return TcLightsManager.ERROR_SESSION_NOT_FOUND;
                }
                session.color = color;
                if (mActiveLightSession != null && mActiveLightSession.id == session.id) {
                    mActiveLightSession = session;
                    setColorInternal(color);
                }
                return session.id;
            }
        }

        @Override
        public int setFlashingById(int id, int color, int onMs, int offMs) {
            synchronized (TcLightsService.this) {
                TcLightSession session = getSessionById(id);
                if (session == null) {
                    return TcLightsManager.ERROR_SESSION_NOT_FOUND;
                }
                session.color = color;
                session.onMs = onMs;
                session.offMs = offMs;
                if (mActiveLightSession != null && mActiveLightSession.id == session.id) {
                    mActiveLightSession = session;
                    setFlashingInternal(color, onMs, offMs);
                }
                return session.id;
            }
        }

        @Override
        public int turnOff(int id) {
            synchronized (TcLightsService.this) {
                //If remove device error led, must shutdown the timer.
                if (id == TcLightsManager.TYPE_LED_DEVICE_ERROR) {
                    stopDeviceErrorExecutor();
                }
                removeSessionById(id);
                mActiveLightSession = getActivePrioritySession();
                if (mActiveLightSession == null) {
                    turnOffInternal();
                } else {
                    updateActiveLightSession();
                }
                return TcLightsManager.SUCCESS_TURN_OFF;
            }
        }

        @Override
        public int turnOffAll() {
            synchronized (TcLightsService.this) {
                mActiveLightSession = null;
                //When turn off all led, must shutdown the device error timer.
                stopDeviceErrorExecutor();
                mTcLightSessionMap.clear();
                turnOffInternal();
                return TcLightsManager.SUCCESS_TURN_OFF;
            }
        }

        private void setColorInternal(int color) {
            LogicalLight logicalLight = mLightsManager.getLight(LightsManager.LIGHT_ID_NOTIFICATIONS);
            if (logicalLight != null) {
                logicalLight.setColor(color);
            } else {
                Slog.e(TAG, "Get custom light is null when setColorInternal.");
            }
        }

        private void setFlashingInternal(int color, int onMs, int offMs) {
            LogicalLight logicalLight = mLightsManager.getLight(LightsManager.LIGHT_ID_NOTIFICATIONS);
            if (logicalLight != null) {
                logicalLight.setFlashing(color, LogicalLight.LIGHT_FLASH_TIMED, onMs, offMs);
            } else {
                Slog.e(TAG, "Get custom light is null when setFlashingInternal.");
            }
        }

        private void setFlashingDeviceErrorInternal() {
            if (mDeviceErrorExecutor == null || mDeviceErrorExecutor.isShutdown()) {
                mDeviceErrorColor = LED_COLOR_BLUE;
                mDeviceErrorExecutor = Executors.newSingleThreadScheduledExecutor();
                mDeviceErrorExecutor.scheduleWithFixedDelay(() -> {
                    if (mDeviceErrorColor == LED_COLOR_RED) {
                        mDeviceErrorColor = LED_COLOR_BLUE;
                    } else {
                        mDeviceErrorColor = LED_COLOR_RED;
                    }
                    setColor(TcLightsManager.TYPE_LED_DEVICE_ERROR, mDeviceErrorColor);
                }, 0, 1, TimeUnit.SECONDS);
            }
        }

        private void stopDeviceErrorExecutor() {
            if (mDeviceErrorExecutor != null && !mDeviceErrorExecutor.isShutdown()) {
                mDeviceErrorExecutor.shutdown();
                try {
                    if (!mDeviceErrorExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                        mDeviceErrorExecutor.shutdownNow();
                    }
                } catch (InterruptedException ex) {
                    mDeviceErrorExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }

        private void turnOffInternal() {
            LogicalLight logicalLight = mLightsManager.getLight(LightsManager.LIGHT_ID_NOTIFICATIONS);
            if (logicalLight != null) {
                logicalLight.turnOff();
            } else {
                Slog.e(TAG, "Get custom light is null when turnOffInternal.");
            }
        }
    }

    /**
     * Create TcLightsService.
     *
     * @param context context
     */
    public TcLightsService(Context context) {
        super(context);
        mLightsManager = getLocalService(LightsManager.class);
        mManagerService = new TcLightsManagerBinderService();
    }

    @Override
    public void onStart() {
        publishBinderService(Context.TC_LIGHTS_SERVICE, mManagerService);
    }

    @Override
    public void onBootPhase(int phase) {
    }
}
