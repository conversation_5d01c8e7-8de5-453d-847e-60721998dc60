/*
 * Copyright (C) 2011 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware;

import android.os.ParcelFileDescriptor;

/** @hide */
interface ISerialManager
{
    /* Returns a list of all available serial ports */
    @EnforcePermission("SERIAL_PORT")
    String[] getSerialPorts();

    /* Returns a file descriptor for the serial port. */
    @EnforcePermission("SERIAL_PORT")
    ParcelFileDescriptor openSerialPort(String name);
}
