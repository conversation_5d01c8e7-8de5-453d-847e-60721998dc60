{
  "imports": [
    {
      // Also includes cts/tests/tests/net
      "path": "frameworks/base/tests/net"
    },
    {
      "path": "packages/modules/NetworkStack"
    },
    {
      "path": "packages/modules/CaptivePortalLogin"
    },
    {
      "path": "frameworks/base/packages/Tethering"
    },
    {
      "path": "frameworks/opt/net/wifi"
    }
  ],
  "presubmit": [
    {
      "name": "FrameworksCoreTests",
      "options": [
        {
          "include-filter": "android.net"
        },
        {
          "include-annotation": "android.platform.test.annotations.Presubmit"
        },
        {
          "exclude-annotation": "androidx.test.filters.FlakyTest"
        },
        {
          "exclude-annotation": "org.junit.Ignore"
        }
      ]
    }
  ]
}
