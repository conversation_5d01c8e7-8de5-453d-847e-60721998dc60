/*
 * Copyright (C) 2014 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.content;

import android.content.Intent;
import android.os.Bundle;
import android.os.PersistableBundle;

/**
 * Interface used by the RestrictionsManager
 * @hide
 */
interface IRestrictionsManager {
    Bundle getApplicationRestrictions(in String packageName);
    boolean hasRestrictionsProvider();
    void requestPermission(in String packageName, in String requestType, in String requestId,
            in PersistableBundle requestData);
    void notifyPermissionResponse(in String packageName, in PersistableBundle response);
    Intent createLocalApprovalIntent();
}
