/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os.strictmode;

import android.annotation.NonNull;
import android.content.Context;

/**
 * Incorrect usage of {@link Context}, such as obtaining a UI service from non-UI {@link Context}
 * instance.
 *
 * @see Context#getSystemService(String)
 * @see Context#isUiContext
 * @see android.os.StrictMode.VmPolicy.Builder#detectIncorrectContextUse()
 */
public final class IncorrectContextUseViolation extends Violation {

    public IncorrectContextUseViolation(@NonNull String message, @NonNull Throwable originStack) {
        super(message);
        initCause(originStack);
    }
}
