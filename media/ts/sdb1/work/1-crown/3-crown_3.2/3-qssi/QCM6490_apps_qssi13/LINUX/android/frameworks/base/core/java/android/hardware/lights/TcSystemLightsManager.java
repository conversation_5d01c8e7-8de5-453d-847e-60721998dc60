package android.hardware.lights;

import android.annotation.NonNull;
import android.content.Context;
import android.os.CountDownTimer;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.os.ServiceManager.ServiceNotFoundException;
import android.util.Slog;

import com.android.internal.annotations.VisibleForTesting;
import com.android.internal.util.Preconditions;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * The TcLightsManager class allows control over device lights.
 *
 * @hide
 */
public class TcSystemLightsManager extends TcLightsManager {

    private static final String TAG = "TcLightsManager";

    private static final int MS_1500 = 1500;
    private static final int MS_1000 = 1000;
    private static final int MS_500 = 500;

    @NonNull
    private final ITcLightsManager mService;

    /**
     * Creates a TcSystemLightsManager.
     *
     * @hide
     */
    public TcSystemLightsManager(@NonNull Context context) throws ServiceNotFoundException {
        this(context, ITcLightsManager.Stub.asInterface(
                ServiceManager.getServiceOrThrow(Context.TC_LIGHTS_SERVICE)));
    }

    /**
     * Creates a TcSystemLightsManager with a provided service implementation.
     *
     * @hide
     */
    @VisibleForTesting
    public TcSystemLightsManager(@NonNull Context context, @NonNull ITcLightsManager service) {
        super(context);
        mService = Preconditions.checkNotNull(service);
        Slog.i(TAG, "TcSystemLightsManager created");
    }

    @Override
    public int setColor(int color) {
        return setColor(TYPE_LED_SLEEP, color);
    }

    @Override
    public int setFlashing(int color, int onMs, int offMs) {
        return setFlashing(TYPE_LED_SLEEP, color, onMs, offMs);
    }

    @Override
    public int turnOffAllLed() {
        return turnOffAll();
    }

    private int setColor(int priority, int color) {
        try {
            return mService.setColor(priority, color);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int setFlashing(int priority, int color, int onMs, int offMs) {
        try {
            return mService.setFlashing(priority, color, onMs, offMs);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int setFlashingDeviceError(int priority) {
        try {
            return mService.setFlashingDeviceError(priority);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int setColorById(int id, int color) {
        try {
            return mService.setColorById(id, color);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int setFlashingById(int id, int color, int onMs, int offMs) {
        try {
            return mService.setFlashingById(id, color, onMs, offMs);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int turnOff(int id) {
        try {
            return mService.turnOff(id);
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    private int turnOffAll() {
        try {
            return mService.turnOffAll();
        } catch (RemoteException ex) {
            throw ex.rethrowFromSystemServer();
        }
    }

    /**
     * Handle led control event.
     *
     * @param eventType event type
     * @param state     state on/off
     */
    @Override
    public void handleLedControlEvent(int eventType, int state) {
        Slog.i(TAG, "handleEvent: eventType = " + eventType);
        switch (eventType) {
            case TYPE_LED_POWER_ON_OFF:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_POWER_ON_OFF, LED_COLOR_GREEN, MS_1000, MS_1000);
                } else {
                    turnOff(TYPE_LED_POWER_ON_OFF);
                }
                break;
            case TYPE_LED_RECORDING:
                if (state == STATE_LED_ON) {
                    setColor(TYPE_LED_RECORDING, LED_COLOR_GREEN);
                } else {
                    turnOff(TYPE_LED_RECORDING);
                }
                break;
            case TYPE_LED_RECORDING_LTE_WEAK:
                if (state == STATE_LED_ON) {
                    setColor(TYPE_LED_RECORDING_LTE_WEAK, LED_COLOR_BLUE);
                } else {
                    turnOff(TYPE_LED_RECORDING_LTE_WEAK);
                }
                break;
            case TYPE_LED_RECORDING_LTE_OUT:
                if (state == STATE_LED_ON) {
                    setColor(TYPE_LED_RECORDING_LTE_OUT, LED_COLOR_RED);
                } else {
                    turnOff(TYPE_LED_RECORDING_LTE_OUT);
                }
                break;
            case TYPE_LED_PROCESSING:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_PROCESSING, LED_COLOR_GREEN, MS_500, MS_1500);
                } else {
                    turnOff(TYPE_LED_PROCESSING);
                }
                break;
            case TYPE_LED_PROCESSING_LTE_WEAK:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_PROCESSING_LTE_WEAK, LED_COLOR_BLUE, MS_500, MS_1500);
                } else {
                    turnOff(TYPE_LED_PROCESSING_LTE_WEAK);
                }
                break;
            case TYPE_LED_PROCESSING_LTE_OUT:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_PROCESSING_LTE_OUT, LED_COLOR_RED, MS_500, MS_1500);
                } else {
                    turnOff(TYPE_LED_PROCESSING_LTE_OUT);
                }
                break;
            case TYPE_LED_DEVICE_WARNING:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_DEVICE_WARNING, LED_COLOR_BLUE, MS_1000, MS_1000);
                } else {
                    turnOff(TYPE_LED_DEVICE_WARNING);
                }
                break;
            case TYPE_LED_SDCARD_ERROR:
                if (state == STATE_LED_ON) {
                    setFlashing(TYPE_LED_SDCARD_ERROR, LED_COLOR_RED, MS_1000, MS_1000);
                } else {
                    turnOff(TYPE_LED_SDCARD_ERROR);
                }
                break;
            case TYPE_LED_DEVICE_ERROR:
                //Special case, turn on/off led device error.
                if (state == STATE_LED_ON) {
                    setFlashingDeviceError(TYPE_LED_DEVICE_ERROR);
                } else {
                    turnOff(TYPE_LED_DEVICE_ERROR);
                }
                break;
            case TYPE_LED_SLEEP:
                if (state == STATE_LED_ON) {
                    setColor(TYPE_LED_SLEEP, LED_COLOR_BLACK);
                } else {
                    turnOff(TYPE_LED_SLEEP);
                }
                break;
            default:
                break;
        }
    }
}
