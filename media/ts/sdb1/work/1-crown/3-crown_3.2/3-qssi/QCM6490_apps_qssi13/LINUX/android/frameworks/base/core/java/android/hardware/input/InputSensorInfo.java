/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware.input;

import android.annotation.NonNull;
import android.os.Parcel;
import android.os.Parcelable;

import com.android.internal.util.DataClass;
/**
 * This class represents a motion sensor for input devices.
 *
 * @hide
 */
@DataClass(
        genToString = true,
        genHiddenConstructor = true,
        genHiddenConstDefs = true)
public class InputSensorInfo implements Parcelable {

    private @NonNull String  mName;
    private @NonNull String  mVendor;
    private int     mVersion;
    private int     mHandle;
    private int     mType;
    private float   mMaxRange;
    private float   mResolution;
    private float   mPower;
    private int     mMinDelay;
    private int     mFifoReservedEventCount;
    private int     mFifoMaxEventCount;
    private @NonNull String  mStringType;
    private @NonNull String  mRequiredPermission;
    private int     mMaxDelay;
    private int     mFlags;
    private int     mId;



    // Code below generated by codegen v1.0.20.
    //
    // DO NOT MODIFY!
    // CHECKSTYLE:OFF Generated code
    //
    // To regenerate run:
    // $ codegen $ANDROID_BUILD_TOP/frameworks/base/core/java/android/hardware/input/InputSensorInfo.java
    //
    // To exclude the generated code from IntelliJ auto-formatting enable (one-time):
    //   Settings > Editor > Code Style > Formatter Control
    //@formatter:off


    /**
     * Creates a new InputSensorInfo.
     *
     * @hide
     */
    @DataClass.Generated.Member
    public InputSensorInfo(
            @NonNull String name,
            @NonNull String vendor,
            int version,
            int handle,
            int type,
            float maxRange,
            float resolution,
            float power,
            int minDelay,
            int fifoReservedEventCount,
            int fifoMaxEventCount,
            @NonNull String stringType,
            @NonNull String requiredPermission,
            int maxDelay,
            int flags,
            int id) {
        this.mName = name;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mName);
        this.mVendor = vendor;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mVendor);
        this.mVersion = version;
        this.mHandle = handle;
        this.mType = type;
        this.mMaxRange = maxRange;
        this.mResolution = resolution;
        this.mPower = power;
        this.mMinDelay = minDelay;
        this.mFifoReservedEventCount = fifoReservedEventCount;
        this.mFifoMaxEventCount = fifoMaxEventCount;
        this.mStringType = stringType;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mStringType);
        this.mRequiredPermission = requiredPermission;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mRequiredPermission);
        this.mMaxDelay = maxDelay;
        this.mFlags = flags;
        this.mId = id;

        // onConstructed(); // You can define this method to get a callback
    }

    @DataClass.Generated.Member
    public @NonNull String getName() {
        return mName;
    }

    @DataClass.Generated.Member
    public @NonNull String getVendor() {
        return mVendor;
    }

    @DataClass.Generated.Member
    public int getVersion() {
        return mVersion;
    }

    @DataClass.Generated.Member
    public int getHandle() {
        return mHandle;
    }

    @DataClass.Generated.Member
    public int getType() {
        return mType;
    }

    @DataClass.Generated.Member
    public float getMaxRange() {
        return mMaxRange;
    }

    @DataClass.Generated.Member
    public float getResolution() {
        return mResolution;
    }

    @DataClass.Generated.Member
    public float getPower() {
        return mPower;
    }

    @DataClass.Generated.Member
    public int getMinDelay() {
        return mMinDelay;
    }

    @DataClass.Generated.Member
    public int getFifoReservedEventCount() {
        return mFifoReservedEventCount;
    }

    @DataClass.Generated.Member
    public int getFifoMaxEventCount() {
        return mFifoMaxEventCount;
    }

    @DataClass.Generated.Member
    public @NonNull String getStringType() {
        return mStringType;
    }

    @DataClass.Generated.Member
    public @NonNull String getRequiredPermission() {
        return mRequiredPermission;
    }

    @DataClass.Generated.Member
    public int getMaxDelay() {
        return mMaxDelay;
    }

    @DataClass.Generated.Member
    public int getFlags() {
        return mFlags;
    }

    @DataClass.Generated.Member
    public int getId() {
        return mId;
    }

    @Override
    @DataClass.Generated.Member
    public String toString() {
        // You can override field toString logic by defining methods like:
        // String fieldNameToString() { ... }

        return "InputSensorInfo { " +
                "name = " + mName + ", " +
                "vendor = " + mVendor + ", " +
                "version = " + mVersion + ", " +
                "handle = " + mHandle + ", " +
                "type = " + mType + ", " +
                "maxRange = " + mMaxRange + ", " +
                "resolution = " + mResolution + ", " +
                "power = " + mPower + ", " +
                "minDelay = " + mMinDelay + ", " +
                "fifoReservedEventCount = " + mFifoReservedEventCount + ", " +
                "fifoMaxEventCount = " + mFifoMaxEventCount + ", " +
                "stringType = " + mStringType + ", " +
                "requiredPermission = " + mRequiredPermission + ", " +
                "maxDelay = " + mMaxDelay + ", " +
                "flags = " + mFlags + ", " +
                "id = " + mId +
        " }";
    }

    @Override
    @DataClass.Generated.Member
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        // You can override field parcelling by defining methods like:
        // void parcelFieldName(Parcel dest, int flags) { ... }

        dest.writeString(mName);
        dest.writeString(mVendor);
        dest.writeInt(mVersion);
        dest.writeInt(mHandle);
        dest.writeInt(mType);
        dest.writeFloat(mMaxRange);
        dest.writeFloat(mResolution);
        dest.writeFloat(mPower);
        dest.writeInt(mMinDelay);
        dest.writeInt(mFifoReservedEventCount);
        dest.writeInt(mFifoMaxEventCount);
        dest.writeString(mStringType);
        dest.writeString(mRequiredPermission);
        dest.writeInt(mMaxDelay);
        dest.writeInt(mFlags);
        dest.writeInt(mId);
    }

    @Override
    @DataClass.Generated.Member
    public int describeContents() { return 0; }

    /** @hide */
    @SuppressWarnings({"unchecked", "RedundantCast"})
    @DataClass.Generated.Member
    protected InputSensorInfo(@NonNull Parcel in) {
        // You can override field unparcelling by defining methods like:
        // static FieldType unparcelFieldName(Parcel in) { ... }

        String name = in.readString();
        String vendor = in.readString();
        int version = in.readInt();
        int handle = in.readInt();
        int type = in.readInt();
        float maxRange = in.readFloat();
        float resolution = in.readFloat();
        float power = in.readFloat();
        int minDelay = in.readInt();
        int fifoReservedEventCount = in.readInt();
        int fifoMaxEventCount = in.readInt();
        String stringType = in.readString();
        String requiredPermission = in.readString();
        int maxDelay = in.readInt();
        int flags = in.readInt();
        int id = in.readInt();

        this.mName = name;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mName);
        this.mVendor = vendor;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mVendor);
        this.mVersion = version;
        this.mHandle = handle;
        this.mType = type;
        this.mMaxRange = maxRange;
        this.mResolution = resolution;
        this.mPower = power;
        this.mMinDelay = minDelay;
        this.mFifoReservedEventCount = fifoReservedEventCount;
        this.mFifoMaxEventCount = fifoMaxEventCount;
        this.mStringType = stringType;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mStringType);
        this.mRequiredPermission = requiredPermission;
        com.android.internal.util.AnnotationValidations.validate(
                NonNull.class, null, mRequiredPermission);
        this.mMaxDelay = maxDelay;
        this.mFlags = flags;
        this.mId = id;

        // onConstructed(); // You can define this method to get a callback
    }

    @DataClass.Generated.Member
    public static final @NonNull Parcelable.Creator<InputSensorInfo> CREATOR
            = new Parcelable.Creator<InputSensorInfo>() {
        @Override
        public InputSensorInfo[] newArray(int size) {
            return new InputSensorInfo[size];
        }

        @Override
        public InputSensorInfo createFromParcel(@NonNull Parcel in) {
            return new InputSensorInfo(in);
        }
    };

    @DataClass.Generated(
            time = 1605294854951L,
            codegenVersion = "1.0.20",
            sourceFile = "frameworks/base/core/java/android/hardware/input/InputSensorInfo.java",
            inputSignatures = "private @android.annotation.NonNull java.lang.String mName\nprivate @android.annotation.NonNull java.lang.String mVendor\nprivate  int mVersion\nprivate  int mHandle\nprivate  int mType\nprivate  float mMaxRange\nprivate  float mResolution\nprivate  float mPower\nprivate  int mMinDelay\nprivate  int mFifoReservedEventCount\nprivate  int mFifoMaxEventCount\nprivate @android.annotation.NonNull java.lang.String mStringType\nprivate @android.annotation.NonNull java.lang.String mRequiredPermission\nprivate  int mMaxDelay\nprivate  int mFlags\nprivate  int mId\nclass InputSensorInfo extends java.lang.Object implements [android.os.Parcelable]\<EMAIL>(genToString=true, genHiddenConstructor=true, genHiddenConstDefs=true)")
    @Deprecated
    private void __metadata() {}


    //@formatter:on
    // End of generated code

}
