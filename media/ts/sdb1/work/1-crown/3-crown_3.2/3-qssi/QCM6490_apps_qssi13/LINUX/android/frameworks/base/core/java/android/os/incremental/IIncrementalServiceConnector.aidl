/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os.incremental;

/** @hide */
interface IIncrementalServiceConnector {
    /**
     * Changes storage params. Returns 0 on success, and -errno on failure.
     * Use enableReadLogs to switch pages read logs reporting on and off.
     * Returns 0 on success, and - errno on failure: permission check or remount.
     */
    int setStorageParams(boolean enableReadLogs);
}
