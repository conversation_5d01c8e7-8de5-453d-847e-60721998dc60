<HTML>
<BODY>
Contains classes to explore data returned through a content provider.
<p>
If you need to manage data in a private database, use the {@link
android.database.sqlite} classes. These classes are used to manage the {@link
android.database.Cursor} object returned from a content provider query. Databases
are usually created and opened with {@link android.content.Context#openOrCreateDatabase}
To make requests through
content providers, you can use the {@link android.content.ContentResolver
content.ContentResolver} class.
<p>All databases are stored on the device in <code>/data/data/&lt;package_name&gt;/databases</code>
</BODY>
</HTML>
