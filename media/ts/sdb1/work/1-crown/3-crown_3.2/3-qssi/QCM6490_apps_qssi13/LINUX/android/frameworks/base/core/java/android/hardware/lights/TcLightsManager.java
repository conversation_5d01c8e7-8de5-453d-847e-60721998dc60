package android.hardware.lights;

import android.content.Context;

import android.annotation.NonNull;
import android.annotation.SystemService;

import com.android.internal.util.Preconditions;


@SystemService(Context.TC_LIGHTS_SERVICE)
public abstract class TcLightsManager {
    @NonNull private final Context mContext;
    /**
     * Min priority.
     * @hide
     */
    public static final int MIN_PRIORITY = 0;

    /**
     * TYPE_LED_POWER_ON_OFF.
     * Mode: Flashing (in:1000ms; out:1000ms).
     * Color: Green.
     */
    public static final int TYPE_LED_POWER_ON_OFF = 0x01;

    /**
     * TYPE_LED_RECORDING.
     * Mode: Color.
     * Color: Green.
     */
    public static final int TYPE_LED_RECORDING = 0x02;

    /**
     * TYPE_LED_RECORDING_LTE_WEAK.
     * Mode: Color.
     * Color: BLUE.
     */
    public static final int TYPE_LED_RECORDING_LTE_WEAK = 0x03;

    /**
     * TYPE_LED_RECORDING_LTE_OUT.
     * Mode: Color.
     * Color: RED.
     */
    public static final int TYPE_LED_RECORDING_LTE_OUT = 0x04;

    /**
     * TYPE_LED_PROCESSING.
     * Mode: Flashing (in:500ms; out:1500ms).
     * Color: GREEN.
     */
    public static final int TYPE_LED_PROCESSING = 0x05;

    /**
     * TYPE_LED_PROCESSING_LTE_WEAK.
     * Mode: Flashing (in:500ms; out:1500ms).
     * Color: BLUE.
     */
    public static final int TYPE_LED_PROCESSING_LTE_WEAK = 0x06;

    /**
     * TYPE_LED_PROCESSING_LTE_OUT.
     * Mode: Flashing (in:500ms; out:1500ms).
     * Color: RED.
     */
    public static final int TYPE_LED_PROCESSING_LTE_OUT = 0x07;

    /**
     * TYPE_LED_DEVICE_WARNING.
     * Mode: Flashing (in:1000ms; out:1000ms).
     * Color: BLUE.
     */
    public static final int TYPE_LED_DEVICE_WARNING = 0x08;

    /**
     * TYPE_LED_SDCARD_ERROR.
     * Mode: Flashing (in:1000ms; out:1000ms).
     * Color: RED.
     */
    public static final int TYPE_LED_SDCARD_ERROR = 0x09;

    /**
     * TYPE_LED_DEVICE_ERROR.
     * Mode: Flashing (in:1000ms; out:1000ms).
     * Color: RED and BLUE.
     */
    public static final int TYPE_LED_DEVICE_ERROR = 0x0A;

    /**
     * TYPE_LED_SLEEP.
     * Mode: Color.
     * Color: BLACK.
     */
    public static final int TYPE_LED_SLEEP = 0x0B;

    public static final int STATE_LED_ON = 0x01;
    public static final int STATE_LED_OFF = 0x00;

    public static final int LED_COLOR_RED = 0xFF0000;
    public static final int LED_COLOR_GREEN = 0x00FF00;
    public static final int LED_COLOR_BLUE = 0x0000FF;
    public static final int LED_COLOR_YELLOW = 0xFFFF00;
    public static final int LED_COLOR_BLACK = 0x000000;

    /**
     * Max session count.
     * @hide
     */
    public static final int MAX_SESSION = 100;
    public static final int ERROR_UNKNOWN = -100;
    public static final int ERROR_PRIORITY_MUST_MORE_THAN_ONE = -99;
    public static final int ERROR_TOO_MANY_SESSION = -98;
    public static final int ERROR_SESSION_NOT_FOUND = -97;
    public static final int FAILED_SET_COLOR = -1;
    public static final int FAILED_SET_FLASHING = -2;
    public static final int FAILED_TURN_OFF = -3;
    public static final int SUCCESS_TURN_OFF = 0;

    /**
     * @hide to prevent subclassing from outside of the framework.
     */
    public TcLightsManager(Context context) {
        mContext = Preconditions.checkNotNull(context);
    }

    /**
     * Handle led control event.
     *
     * @param eventType event type
     * @param state state (ON/OFF)
     */
    public abstract void handleLedControlEvent(int eventType, int state);

    /**
     * Set color.
     *
     * @param color color
     * @return result code
     */
    public abstract int setColor(int color);

    /**
     * Set flashing.
     *
     * @param color color
     * @param onMs on duration in milliseconds
     * @param offMs off duration in milliseconds
     * @return result code
     */
    public abstract int setFlashing(int color, int onMs, int offMs);

    /**
     * Turn off all LED.
     *
     * @return result code for turn off all LEDs
     */
    public abstract int turnOffAllLed();
}
